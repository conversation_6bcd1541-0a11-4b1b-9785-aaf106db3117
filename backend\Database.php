<?php
include 'config.php';

// Function to handle file upload
function uploadFile($file, $uploadDir = '.assets/images/uploads/') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }

    // Create uploads directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Get file extension
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    // Allowed file types
    $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    if (!in_array($fileExtension, $allowedTypes)) {
        throw new Exception("Invalid file type. Only JPG, JPEG, PNG, GIF, and WEBP files are allowed.");
    }

    // Check file size (5MB limit)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception("File size too large. Maximum size is 5MB.");
    }

    // Generate unique filename
    $uniqueFilename = time() . '_' . basename($file['name']);
    $targetPath = $uploadDir . $uniqueFilename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return $uniqueFilename;
    } else {
        throw new Exception("Failed to upload file.");
    }
}

// Function to validate input data
function validateInput($data) {
    $errors = [];

    if (empty(trim($data['name']))) {
        $errors[] = "Name is required.";
    }

    if (empty(trim($data['phone']))) {
        $errors[] = "Phone number is required.";
    } elseif (!preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', trim($data['phone']))) {
        $errors[] = "Please enter a valid phone number.";
    }

    return $errors;
}

// Function to save data to database
function saveToDatabase($pdo, $name, $phone, $file1Path, $file2Path) {
    $sql = "INSERT INTO user_submissions (name, phone, file1_path, file2_path, created_at) VALUES (?, ?, ?, ?, NOW())";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$name, $phone, $file1Path, $file2Path]);
}

// Main processing logic
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate input data
        $errors = validateInput($_POST);

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // Sanitize input data
        $name = trim($_POST['name']);
        $phone = trim($_POST['phone']);

        // Handle file uploads
        $file1Path = null;
        $file2Path = null;

        if (isset($_FILES['file1']) && $_FILES['file1']['error'] === UPLOAD_ERR_OK) {
            $file1Path = uploadFile($_FILES['file1']);
        }

        if (isset($_FILES['file2']) && $_FILES['file2']['error'] === UPLOAD_ERR_OK) {
            $file2Path = uploadFile($_FILES['file2']);
        }

        // Save to database
        if (saveToDatabase($pdo, $name, $phone, $file1Path, $file2Path)) {
            $successMessage = "Data saved successfully!";
            if ($file1Path) $successMessage .= "<br>File 1: " . htmlspecialchars($file1Path);
            if ($file2Path) $successMessage .= "<br>File 2: " . htmlspecialchars($file2Path);
        } else {
            throw new Exception("Failed to save data to database.");
        }

    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
    }
} else {
    $errorMessage = "Invalid request method.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submission Result</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-6">
    <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <?php if (isset($successMessage)): ?>
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                <h2 class="text-xl font-bold mb-2">Success!</h2>
                <p><?php echo $successMessage; ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
            <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <h2 class="text-xl font-bold mb-2">Error!</h2>
                <p><?php echo htmlspecialchars($errorMessage); ?></p>
            </div>
        <?php endif; ?>

        <a href="../index.php" class="inline-block bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
            Go Back
        </a>
    </div>
</body>
</html>