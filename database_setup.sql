-- Database setup script for the form submission system
-- Run this script in your MySQL database to create the required table

CREATE DATABASE IF NOT EXISTS your_database_name;
USE your_database_name;

CREATE TABLE IF NOT EXISTS user_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    file1_path VARCHAR(500) NULL,
    file2_path VARCHAR(500) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Optional: Create an index on created_at for better query performance
CREATE INDEX idx_created_at ON user_submissions(created_at);

-- Sample query to view all submissions
-- SELECT * FROM user_submissions ORDER BY created_at DESC;
